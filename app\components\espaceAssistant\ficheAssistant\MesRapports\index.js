import React, { useCallback, useEffect, useMemo, useState } from 'react';
import styles from 'Css/profileList.css';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { Link, useHistory,use } from 'react-router-dom';
import { loadRapportByAssistant } from 'containers/EspaceAssistant/FicheAssistant/actions';
import {
  getStatusName,
  getStatusStyle,
} from 'components/Beneficiary/Profile/BeneficiaryRapport/statutUtils';
import { useHasRole } from 'utils/hasAccess';
import { createStructuredSelector } from 'reselect';
import {
  makeSelectConnectedAssistant,
  makeSelectRapportByAssistant,
} from 'containers/EspaceAssistant/FicheAssistant/selectors';
import request from 'utils/request';
import { openBlobInNewTab } from 'utils/utilFuncs/blobUtils';
import { ARTICLE_ICON, VIEW_ICON } from '../../../Common/ListIcons/ListIcons';
import CustomPagination from '../../../Common/CustomPagination';
import DataTable from '../../../Common/DataTable';
import GenericFilter from 'containers/Common/Filter/GenericFilter';
import Modal2 from 'components/Common/Modal2';
import btnStyles from '../../../../Css/button.css';

const executeViewApi = async ({ id, onLoad, onError, onSuccess }) => {
  try {
    onLoad(true);
    const url = `rapport/view-report/${id}`;
    console.log({ rapportAPICALLED: id });
    const { data, headers } = await request.get(url, {
      responseType: 'blob',
    });

    openBlobInNewTab(data, '', headers['content-disposition']);
    onSuccess(true);
    onLoad(false);

    return;
  } catch (error) {
    console.error('Error viewing the report:', error);
    onSuccess(true);
    onLoad(false);
    onError(false);
  }
};
const mesRapportsStoreStates = createStructuredSelector({
  rapportByAssistant: makeSelectRapportByAssistant,
  connectedAssistant: makeSelectConnectedAssistant,
});

export default function MesRapport() {
  const dispatch = useDispatch();
  const { rapportByAssistant, connectedAssistant } = useSelector(
    mesRapportsStoreStates,
  );
  const params = useParams();
  const connectedAssistantId = params.id;
  const formatDate = date => moment(date).format('DD/MM/YYYY');
  const [showAlert, setShowAlert] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const hasRoleKafalat = useHasRole('GESTIONNAIRE_KAFALAT');
  const hasRoleMarketing = useHasRole('GESTIONNAIRE_MARKETING');
  const hasRoleAssistant = useHasRole('ASSISTANT');
  const hasRoleAdmin = useHasRole('ADMIN');
  const history = useHistory();

  const [currentPage, setCurrentPage] = useState(0);
  const [showSelectLangRapportModal, setShowSelectLangRapportModal] = useState(
    false,
  );
  const [reportId, setReportId] = useState(null);
  const [chosenLanguage, setChosenLanguage] = useState(null);
  const [errorsLangueInfo, setErrorsLangueInfo] = useState({
    selection: {
      isError: false,
      errorMessage: 'Veuillez choisir une langue',
    },
  });

  const principalInputsConfig = [
    {
      field: 'reportStatus',
      type: 'select',
      placeholder: 'Statut du rapport',
      options: [
        { value: 'RAPPORT_PLANIFIER', label: 'Planifié' },
        { value: 'RAPPORT_A_PREPARE', label: 'À préparer' },
        { value: 'RAPPORT_INITIAL', label: 'Initial' },
        {
          value: 'RAPPORT_VALIDER_ASSISTANCE',
          label: "Validé par l'assistant",
        },
        { value: 'RAPPORT_VALIDER_KAFALAT', label: 'Validé par Kafalat' },
        {
          value: 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE',
          label: "Rapport à compléter par l'assistant",
        },
        {
          value: 'RAPPORT_A_COMPLETER_PAR_KAFALAT',
          label: 'Rapport à compléter par Kafalat',
        },
        { value: 'RAPPORT_FINAL', label: 'Final' },
      ],
      widthStyles: {
        width: 250,
      },
    },
    {
      field: 'beneficiaryCode',
      type: 'text',
      placeholder: 'Code Bénéficiaire',
    },
  ];
  const additionalInputsConfig = [
    {
      field: 'beneficiaryName',
      type: 'text',
      placeholder: 'Nom Bénéficiaire',
    },
    {
      field: 'numberRapport',
      type: 'number',
      placeholder: 'Numéro du Rapport',
    },
    {
      field: 'plannedDateStart',
      type: 'date',
      placeholder: 'Date de début de planification',
    },
    {
      field: 'plannedDateEnd',
      type: 'date',
      placeholder: 'Date de fin de planification',
    },
    {
      field: 'validationDateStart',
      type: 'date',
      placeholder: 'Date de début de validation',
    },
    {
      field: 'validationDateEnd',
      type: 'date',
      placeholder: 'Date de fin de validation',
    },
  ];

  const [filterValues, setFilterValues] = useState({
    beneficiaryCode: '',
    beneficiaryName: '',
    numberRapport: '',
    reportStatus: '',
    plannedDateStart: '',
    plannedDateEnd: '',
    validationDateStart: '',
    validationDateEnd: '',
  });

  const handleResetFilterComplete = () => {
    setFilterValues({
      beneficiaryCode: '',
      beneficiaryName: '',
      numberRapport: '',
      reportStatus: '',
      plannedDateStart: '',
      plannedDateEnd: '',
      validationDateStart: '',
      validationDateEnd: '',
    });
  };

  const handlePageChange = pageNumber => {
    setCurrentPage(pageNumber - 1);
    setFilterValues(prevFilterValues => {
      dispatch(
        loadRapportByAssistant(
          connectedAssistantId,
          pageNumber - 1,
          prevFilterValues,
        ),
      );
      return prevFilterValues;
    });
  };

  const handleApplyFilter = filters => {
    dispatch(loadRapportByAssistant(connectedAssistantId, 0, filters));
  };
  useEffect(() => {
    if (showAlert) {
      const timer = setTimeout(() => setShowAlert(false), 4000);
      return () => clearTimeout(timer);
    }
  }, [showAlert]);

  useEffect(() => {
    if (showErrorAlert) {
      const timer = setTimeout(() => setShowErrorAlert(false), 4000);
      return () => clearTimeout(timer);
    }
  }, [showErrorAlert]);

  useEffect(() => {
    dispatch(loadRapportByAssistant(connectedAssistantId, currentPage));
  }, [connectedAssistantId]);

  const getStatusAssistance = statut => {
    if (
      hasRoleAssistant &&
      (statut === 'RAPPORT_VALIDER_ASSISTANCE' ||
        statut === 'RAPPORT_VALIDER_KAFALAT' ||
        statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT')
    ) {
      return 'en cours de validation';
    }
    if (hasRoleAssistant && statut === 'RAPPORT_A_COMPLETER_PAR_ASSISTANCE') {
      return 'à completer';
    }
  };

  const getStatusKafalat = statut => {
    if (hasRoleKafalat && statut === 'RAPPORT_VALIDER_ASSISTANCE') {
      return 'à valider';
    }
    if (hasRoleKafalat && statut === 'RAPPORT_VALIDER_KAFALAT') {
      return 'en cours de validation';
    }
    if (hasRoleKafalat && statut === 'RAPPORT_A_COMPLETER_PAR_KAFALAT') {
      return 'à completer';
    }
  };

  const getStatusMarketing = statut => {
    if (hasRoleMarketing && statut === 'RAPPORT_VALIDER_ASSISTANCE') {
      return 'en cours de validation';
    }
    if (hasRoleMarketing && statut === 'RAPPORT_VALIDER_KAFALAT') {
      return 'à valider';
    }
  };

  const handleSubmitViewRapport = useCallback(() => {
    if (!chosenLanguage) {
      setErrorsLangueInfo({
        selection: {
          errorMessage: 'Veuillez choisir une langue',
          isError: true,
        },
      });
    } else {
      executeViewApi({
        id: `${reportId}/${chosenLanguage}`,
        onLoad: loading => {
          console.log({ loading: loading });
        },
        onSuccess: response => {
          console.log({ response: response });
          setChosenLanguage(null);
        },
        onError: error => {
          console.log({ error: error });
        },
      });
      setShowSelectLangRapportModal(false);
    }
  }, [chosenLanguage, reportId]);

  let listRapport = [];
  listRapport =
    (rapportByAssistant &&
      rapportByAssistant.content &&
      rapportByAssistant.content.map(rapport => ({
        id: rapport.id,
        status: rapport.status ? rapport.status : '-',
        beneficiaryName: rapport.beneficiary
          ? `${rapport.beneficiary.firstName} ${rapport.beneficiary.lastName}`
          : '-',
        beneficiaryCode: rapport.beneficiary
          ? `${rapport.beneficiary.code}`
          : '-',
        datePlanned: rapport.datePlanned
          ? formatDate(rapport.datePlanned)
          : '-',
        dateValidate: rapport.dateValidate
          ? formatDate(rapport.dateValidate)
          : '-',
        beneficiaryId: rapport.beneficiary ? `${rapport.beneficiary.id}` : '-',
        detailComplete: rapport.detailComplete ? rapport.detailComplete : '-',
        numberRapport: rapport.numberRapport
          ? `${rapport.numberRapport} / ${rapport.year}`
          : '-',
        action: { id: rapport.id, ...rapport },

        rapport,
      }))) ||
    [];

  console.log('listRapport', listRapport);
  const columns = useMemo(
    () => [
      {
        field: 'beneficiaryCode',
        headerName: 'Code Bénéficiaire',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
      },
      {
        field: 'beneficiaryName',
        headerName: 'Nom complet du bénéficiaire',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
        renderCell: params => (
          <Link
            to={{
              pathname: `/beneficiaries/fiche/${params.row.beneficiaryId}/info`,
            }}
          >
            {params.row.beneficiaryName}
          </Link>
        ),
      },
      {
        field: 'numberRapport',
        headerName: 'Numéro du Rapport',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
      },
      {
        field: 'status',
        headerName: 'Statut',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
        renderCell: params => {
          const statut = params.row.status;
          const statusName = hasRoleAdmin
            ? getStatusName(statut)
            : hasRoleAssistant
            ? getStatusAssistance(statut) || getStatusName(statut)
            : hasRoleKafalat
            ? getStatusKafalat(statut) || getStatusName(statut)
            : hasRoleMarketing
            ? getStatusMarketing(statut) || getStatusName(statut)
            : getStatusName(statut);
          const statusStyle = getStatusStyle(statut);
          return (
            <span
              className={statusStyle}
              style={{ padding: '5px', borderRadius: '4px' }}
            >
              {statusName}
            </span>
          );
        },
      },
      {
        field: 'datePlanned',
        headerName: 'Date de Planification',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
      },
      {
        field: 'dateValidate',
        headerName: 'Date de validation',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
      },
      {
        field: 'detailComplete',
        headerName: 'Détail du complément',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
      },
      {
        field: 'actions',
        type: 'actions',
        headerName: 'Actions',
        flex: 1,
        headerAlign: 'center',
        align: 'center',
        renderCell: params => (
          <div>
            <input
              type="image"
              src={VIEW_ICON}
              className="p-2"
              width="40px"
              height="40px"
              title="Consulter"
              onClick={() => {
                history.push(
                  `/beneficiaries/fiche/${params.row.beneficiaryId}/rapport`,
                );
              }}
            />
            {params.row.status !== 'RAPPORT_PLANIFIER' &&
              params.row.status !== 'RAPPORT_A_PREPARE' && (
                <>
                  <input
                    type="image"
                    src={ARTICLE_ICON}
                    className="p-2"
                    width="40px"
                    height="40px"
                    title="visualiser"
                    onClick={() => {
                      setReportId(params.row.rapport.rapportId);
                      setShowSelectLangRapportModal(true);
                    }}
                  />
                </>
              )}
          </div>
        ),
      },
    ],
    [hasRoleAdmin],
  );

  return (
    <div>
      <Modal2
        centered
        className="mt-5"
        title="Sélectionner la langue du rapport"
        show={showSelectLangRapportModal}
        handleClose={() => setShowSelectLangRapportModal(false)}
      >
        <div className="mb-3">
          <label htmlFor="statutSelect" className="form-label">
            Veuillez sélectionner la langue dans laquelle vous souhaitez
            visualiser le rapport. <span className="text-danger">*</span>
          </label>
          <select
            className={`form-select custom-select ${
              errorsLangueInfo.selection.isError ? 'is-invalid' : ''
            }`}
            onChange={e => {
              setChosenLanguage(e.target.value);
              if (errorsLangueInfo.selection.isError) {
                setErrorsLangueInfo({
                  selection: {
                    isError: false,
                    errorMessage: 'Veuillez choisir une langue',
                  },
                });
              }
            }}
          >
            <option value="">Sélectionner la langue</option>
            <option value="ar">Arabe</option>
            <option value="fr">Français</option>
            <option value="en">Anglais</option>
          </select>
          {errorsLangueInfo.selection.isError && (
            <div className="text-danger">
              {errorsLangueInfo.selection.errorMessage}
            </div>
          )}
        </div>
        <div className="d-flex justify-content-end px-3 my-1">
          <button
            type="button"
            className={`mx-2 ${btnStyles.cancelBtn}`}
            onClick={() => setShowSelectLangRapportModal(false)}
          >
            Annuler
          </button>
          <button
            type="button"
            className={`mx-2 ${btnStyles.rejectBtn}`}
            onClick={handleSubmitViewRapport}
          >
            Confirmer
          </button>
        </div>
      </Modal2>
      <div className={styles.backgroudStyle}>
        <div className={styles.global}>
          <div className={styles.header}>
            <h4>Liste de rapports Kafalat</h4>
          </div>
          <GenericFilter
            className="col-12"
            principalInputsConfig={principalInputsConfig}
            additionalInputsConfig={additionalInputsConfig}
            onApplyFilter={handleApplyFilter}
            filterValues={filterValues}
            setFilterValues={setFilterValues}
            onResetFilterComplete={handleResetFilterComplete}
          />
          <DataTable rows={listRapport} columns={columns} />

          <div style={{ display: 'flex', justifyContent: 'center' }}>
            {rapportByAssistant && (
              <CustomPagination
                totalCount={
                  Math.ceil(
                    rapportByAssistant &&
                      rapportByAssistant.totalElements &&
                      rapportByAssistant.pageable &&
                      rapportByAssistant.totalElements /
                        rapportByAssistant.pageable.pageSize,
                  ) || 1
                }
                pageSize={
                  (rapportByAssistant.pageable &&
                    rapportByAssistant.pageable.pageSize &&
                    rapportByAssistant.pageable.pageSize) ||
                  10
                }
                currentPage={currentPage + 1 || 1}
                onPageChange={handlePageChange}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
